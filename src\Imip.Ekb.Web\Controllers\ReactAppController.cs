using System;
using System.IO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Imip.Ekb.Web.Controllers
{
    [Authorize]
    public class ReactAppController : Controller
    {
        private readonly IWebHostEnvironment _env;
        private readonly IConfiguration _configuration;
        private readonly bool _useViteDevServer;
        private readonly ILogger<ReactAppController> _logger;

        public ReactAppController(
            IWebHostEnvironment env,
            IConfiguration configuration,
            ILogger<ReactAppController> logger)
        {
            _env = env;
            _configuration = configuration;
            _logger = logger;

            // Check if we should use the Vite dev server (default to true in development)
            _useViteDevServer = _env.IsDevelopment() &&
                               !_configuration.GetValue<bool>("ReactApp:UseBuiltAssetsInDevelopment", false);
        }

        public IActionResult Index()
        {
            // Check if the built React app exists in the wwwroot/spa directory
            var filePath = Path.Combine(_env.WebRootPath, "spa", "index.html");
            bool builtAppExists = System.IO.File.Exists(filePath);

            // Log the file path and existence for debugging
            _logger.LogInformation($"Checking for React app at: {filePath}, Exists: {builtAppExists}");

            // Check if the assets directory exists
            var assetsPath = Path.Combine(_env.WebRootPath, "spa", "assets");
            bool assetsExist = Directory.Exists(assetsPath);
            _logger.LogInformation($"Checking for assets at: {assetsPath}, Exists: {assetsExist}");

            if (assetsExist)
            {
                // List the files in the assets directory
                var assetFiles = Directory.GetFiles(assetsPath);
                _logger.LogInformation($"Found {assetFiles.Length} files in assets directory");
                foreach (var file in assetFiles)
                {
                    _logger.LogInformation($"Asset file: {Path.GetFileName(file)}");
                }
            }

            // In development, check if we should use the built assets or redirect to Vite dev server
            if (_env.IsDevelopment())
            {
                // If the built app exists and we're configured to use it, serve it
                if (builtAppExists && !_useViteDevServer)
                {
                    _logger.LogInformation("Serving built React app from wwwroot/spa directory");

                    // Read the content of the index.html file for debugging
                    var indexContent = System.IO.File.ReadAllText(filePath);
                    _logger.LogInformation($"Index.html content length: {indexContent.Length} bytes");
                    _logger.LogInformation($"Index.html contains base tag: {indexContent.Contains("<base href=\"/spa/\"")}");

                    return PhysicalFile(filePath, "text/html; charset=UTF-8");
                }

                // Otherwise, redirect to the Vite dev server
                _logger.LogInformation("Redirecting to Vite dev server");
                return Redirect("http://localhost:5173/");
            }
            else
            {
                // In production, always serve the built React app
                if (!builtAppExists)
                {
                    _logger.LogWarning("React app not found in wwwroot/spa directory");
                    return NotFound("React app not found. Make sure to build the React app and copy it to the wwwroot/spa directory.");
                }
                _logger.LogInformation("Serving built React app from wwwroot/spa directory (production)");
                return PhysicalFile(filePath, "text/html; charset=UTF-8");
            }
        }
    }
}
