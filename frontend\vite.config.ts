import { mkdirSync } from "fs"
const outDir = '../src/Imip.Ekb.Web/wwwroot/build'
mkdirSync(outDir, { recursive: true })

import path from "path"
import laravel from "laravel-vite-plugin"
import tailwindcss from "@tailwindcss/vite"
import react from "@vitejs/plugin-react"
import { defineConfig } from "vite"

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    laravel({
      input: ['src/App.tsx'],
      publicDirectory: outDir,
      refresh: true,
    }),
    react(), 
    tailwindcss()
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    outDir,
    emptyOutDir: true,
  },
})